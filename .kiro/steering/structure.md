# Project Structure

## Directory Organization

```
├── apis/                    # API layer - single interface encapsulation
│   ├── base_api.py         # Base API class with config management
│   ├── jsonrpc_api.py      # JSON-RPC methods for EVM chains
│   ├── solana_api.py       # Solana-specific RPC methods
│   ├── starknet_api.py     # Starknet-specific RPC methods
│   └── [chain]_api.py      # Chain-specific API implementations
├── common/                  # Shared utilities and tools
│   ├── utils.py            # Core utility functions
│   ├── config_manager.py   # Configuration management with hot reload
│   ├── assertion_utils.py  # Test assertion helpers
│   ├── network_client.py   # HTTP/WebSocket client wrapper
│   └── types.py            # Type definitions
├── conf/                   # Configuration files
│   └── config.yaml         # Main config (URLs, headers, environments)
├── data/                   # Test data management
│   ├── case_data.yaml      # Test case data organized by chain
│   └── test_templates.yaml # Reusable test templates
├── testcases/              # Test implementations
│   ├── test_[chain].py     # Chain-specific test classes
│   └── test_abstract.py    # Abstract test patterns
├── conftest.py             # Pytest configuration and fixtures
├── pyproject.toml          # Project config, dependencies, tool settings
└── run.py                  # Main test runner
```

## Architecture Patterns

### API Layer (`apis/`)
- **Inheritance**: All API classes inherit from `BaseApi` for config management
- **Decorators**: Use `@api_call` decorator for consistent logging and error handling
- **Method Naming**: Follow RPC method names (e.g., `eth_chainId`, `eth_getBalance`)
- **Chain-Specific**: Separate API files for different blockchain protocols

### Test Layer (`testcases/`)
- **Class Structure**: One test class per chain, inheriting from API class
- **Pytest Marks**: Use chain-specific markers (`@pytest.mark.ethereum`, `@pytest.mark.evm`)
- **Parametrization**: Data-driven tests using `@pytest.mark.parametrize`
- **Fixtures**: Chain-scoped fixtures for dynamic data (block hashes, transaction hashes)

### Configuration Management
- **Hot Reload**: Config files automatically reload on changes
- **Environment Support**: `--env` parameter switches between alphanet/stage/testnet
- **Custom URLs**: Support for custom RPC endpoints via command line
- **Caching**: Configuration manager caches parsed YAML for performance

### Data Organization (`data/`)
- **Hierarchical**: Organized by chain → method → test cases
- **Template Support**: Use `$variable` syntax for dynamic values
- **Expected Results**: Each test case includes expected response patterns

## Coding Conventions

### Test Classes
```python
@pytest.mark.chain_name
@pytest.mark.evm  # or appropriate protocol marker
class TestChainName(JsonrpcApi):
    datas = JsonrpcApi.data
    
    @pytest.mark.parametrize('data', datas['chain_name']['method_name'])
    def test_method_name(self, env, data):
        result = self.method_name(env['chain_name'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json()['result'], data['expected'])
```

### API Methods
```python
@api_call
def rpc_method_name(self, url, **data):
    """RPC method description"""
    return self._make_rpc_call(url, **data)
```

### Assertion Patterns
- `Utils.assert_status_and_nodeid()`: Verify HTTP 200 and X-Node-Id header
- `Utils.assert_id_and_version()`: Verify JSON-RPC id and version fields
- `Utils.assert_contains()`: Check response contains expected values
- `Utils.assert_not_contains()`: Ensure response doesn't contain error patterns

## File Naming Conventions
- Test files: `test_[chain_name].py`
- API files: `[chain_name]_api.py`
- Chain names: Use lowercase with underscores (e.g., `arbitrum_sepolia`)
- Test methods: `test_[rpc_method_name]` (e.g., `test_eth_chainId`)

## Adding New Chains
1. Create API file in `apis/` if needed (non-EVM chains)
2. Add test file in `testcases/test_[chain].py`
3. Add pytest marker in `pyproject.toml`
4. Add URL configuration in `conf/config.yaml`
5. Add test data in `data/case_data.yaml`