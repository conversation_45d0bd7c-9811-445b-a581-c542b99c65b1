# Technology Stack

## Build System & Package Management
- **Python**: 3.8+ (supports 3.8, 3.9, 3.10, 3.11)
- **Build System**: setuptools with setuptools_scm for versioning
- **Package Manager**: pip with requirements.txt

## Core Dependencies
- **Testing Framework**: pytest 8.3.3 with pytest-xdist for parallel execution
- **HTTP Client**: httpx 0.27.2 for async/sync HTTP requests
- **WebSocket**: websocket-client 1.8.0 for WebSocket connections
- **Logging**: loguru 0.7.2 for structured logging
- **Configuration**: PyYAML 6.0.2 for YAML config management
- **Type Support**: typing-extensions 4.12.2

## Development Tools
- **Code Formatting**: black (line-length: 100)
- **Import Sorting**: isort with black profile
- **Linting**: flake8 with custom rules
- **Type Checking**: mypy with strict mode for core modules
- **Pre-commit**: pre-commit hooks for code quality

## Common Commands

### Testing
```bash
# Run all tests
pytest

# Run with verbose output
pytest -vv

# Run specific environment
pytest --env alphanet
pytest --env stage
pytest --env testnet

# Run specific chains
pytest -m ethereum
pytest -m "bsc or polygon"
pytest -m "evm and not ethereum"

# Parallel execution
pytest -n 4          # Use 4 cores
pytest -n auto       # Auto-detect cores

# Run with custom URL/IP
pytest --env https://custom-rpc-url.com
```

### Development
```bash
# Install dependencies
pip install -r requirements.txt

# Install dev dependencies
pip install -e ".[dev]"

# Code formatting
black .
isort .

# Type checking
mypy .

# Linting
flake8 .
```

## Configuration Management
- **Hot Reload**: Configuration files support automatic reloading
- **Environment Variables**: Support for custom URLs and IP mode
- **Caching**: Built-in configuration caching for performance
- **Multi-environment**: Seamless switching between alphanet/stage/testnet