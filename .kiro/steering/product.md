# Product Overview

BlockPI RPC Test Framework is an automated testing framework for blockchain RPC interfaces.

## Core Purpose
- Test 50+ blockchain networks including Ethereum, BSC, Polygon, Arbitrum, Solana, Starknet, and more
- Validate RPC method compatibility across different blockchain protocols
- Support multiple environments: alphanet (production), stage (pre-production), testnet

## Key Features
- **Multi-chain Support**: EVM-compatible chains and non-EVM chains (Solana, Starknet, TON, etc.)
- **Protocol Coverage**: HTTP, WebSocket, and Archive node testing
- **Parallel Execution**: Multi-threaded test execution for efficiency
- **Environment Flexibility**: Easy switching between different deployment environments

## Target Users
- Blockchain infrastructure teams
- RPC service providers
- QA engineers testing blockchain integrations
- DevOps teams validating node deployments