{"enabled": true, "name": "Auto Documentation Generator", "description": "Automatically generates complete documentation for Python files including function signatures, parameter descriptions, return value types, usage examples, and updates README.md with new exports", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.py"]}, "then": {"type": "askAgent", "prompt": "为当前修改的Python文件生成完整文档：\n1. 提取所有函数与类的签名，包括参数类型和返回值类型\n2. 为每个参数与返回值撰写详细说明并注明类型\n3. 基于现有代码提供实际的使用示例\n4. 识别新增的导出项（函数、类、常量）并同步更新至 README.md\n5. 确保文档符合项目规范，包括：\n   - 使用Google风格的docstring\n   - 包含类型注解\n   - 提供完整的使用示例\n   - 遵循项目的代码风格（black格式化，100字符行长度）\n6. 如果是API文件，确保文档包含RPC方法的具体用法和响应格式\n7. 如果是测试文件，确保文档说明测试覆盖的场景和断言逻辑\n\n请分析文件内容并生成相应的文档更新。"}}