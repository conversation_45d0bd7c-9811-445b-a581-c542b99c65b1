"""
网络客户端模块

功能：
- HTTP请求处理：支持各种HTTP方法的请求发送
- WebSocket通信：WebSocket连接创建和消息处理
- IP模式支持：集成IP模式处理器
- 请求日志：详细的请求和响应日志记录
- 错误处理：网络异常的捕获和处理

核心类：
- NetworkClient: 网络客户端类

主要功能：
1. HTTP通信：
   - send_http(): 发送HTTP请求
   - 支持GET、POST、PUT、DELETE等方法
   - 自动处理请求头、参数、JSON数据

2. WebSocket通信：
   - websocket_connection(): 创建WebSocket连接
   - send_websocket(): 发送WebSocket消息
   - 自动处理连接异常

3. IP模式集成：
   - 自动检测和处理IP模式
   - SSL验证配置
   - Host头添加

4. 日志记录：
   - 详细的请求日志
   - 响应时间统计
   - 错误信息记录

使用方式：
    from common.network_client import NetworkClient
    client = NetworkClient()
    response = client.send_http(request_data)
"""

import json
import time
import httpx
from loguru import logger
from websocket import create_connection
from common.ip_mode_handler import IPModeHandler


class NetworkClient:
    """
    网络客户端类
    提供HTTP和WebSocket通信功能
    """
    
    def __init__(self, ip_mode_handler: IPModeHandler = None):
        """
        初始化网络客户端
        :param ip_mode_handler: IP模式处理器
        """
        self.ip_mode_handler = ip_mode_handler
    
    def send_http(self, data: dict):
        """
        发送HTTP请求
        :param data: 请求数据, {'method': 'get', 'url': 'xxx', 'headers': {}, 'params': {}, 'json': {}, 'data': {}}
        :return: response
        """
        start_time = time.time()
        response = None

        try:
            # 处理IP模式
            data_to_send = self._process_ip_mode(data)

            # 记录请求日志
            self._log_request(
                data_to_send.get('method', 'get'),
                data_to_send.get('url'),
                data_to_send.get('headers'),
                data_to_send.get('params'),
                data_to_send.get('json'),
                data_to_send.get('data')
            )

            # 设置SSL验证 - 禁用SSL验证以解决代理环境下的连接问题
            verify_ssl = data_to_send.pop('verify', False)  # 默认禁用SSL验证

            # 发送请求，增加超时时间以适应代理环境
            with httpx.Client(verify=verify_ssl, timeout=60) as client:
                response = client.request(**data_to_send)

            # 计算请求耗时
            duration = time.time() - start_time
            logger.info(f"请求成功，耗时: {duration:.3f}秒")

            return response

        except Exception as e:
            # 计算请求耗时（即使失败）
            duration = time.time() - start_time
            logger.error(f"请求失败，耗时: {duration:.3f}秒，错误: {e}")
            raise e

    def websocket_connection(self, url):
        """
        建立WebSocket连接
        :param url: 连接地址
        :return: WebSocket连接对象
        """
        try:
            logger.info(f"建立WebSocket连接: {url}")
            ws = create_connection(url)
            return ws
        except Exception as e:
            logger.exception(f'发生的错误为：{e}')
            raise e

    def send_websocket(self, ws, data):
        """
        发送WebSocket消息
        :param ws: WebSocket连接对象
        :param data: 请求数据
        :return: 响应数据
        """
        try:
            logger.info(f"发送WebSocket消息: {data}")
            ws.send(json.dumps(data))
            response = json.loads(ws.recv())
            logger.info(f"WebSocket响应: {response}")
            return response
        except Exception as e:
            logger.exception(f'WebSocket通信错误：{e}')
            raise e

    def _process_ip_mode(self, data: dict) -> dict:
        """
        处理IP模式
        :param data: 原始请求数据
        :return: 处理后的请求数据
        """
        if self.ip_mode_handler is None:
            return data.copy()
        
        # 使用IP模式处理器处理请求
        processed_data = self.ip_mode_handler.process_request(data)
        return processed_data

    def _log_request(self, method, url, headers=None, params=None, json_data=None, data=None):
        """
        记录请求日志
        :param method: 请求方式
        :param url: 请求地址
        :param headers: 请求头
        :param params: 请求参数
        :param json_data: JSON请求体
        :param data: 表单请求体
        """
        logger.info(f"请求方式：{method}")
        logger.info(f"请求地址：{url}")
        logger.info(f"请求头：{headers}")
        logger.info(f"请求参数：{params}")
        logger.info(f"请求体(json)：{json_data}")
        logger.info(f"请求体(data)：{data}")


# 创建默认的网络客户端实例
default_network_client = NetworkClient()


# 提供便捷的函数接口，保持向后兼容
def send_http(data: dict):
    """发送HTTP请求的便捷函数"""
    return default_network_client.send_http(data)


def websocket_connection(url):
    """建立WebSocket连接的便捷函数"""
    return default_network_client.websocket_connection(url)


def send_websocket(ws, data):
    """发送WebSocket消息的便捷函数"""
    return default_network_client.send_websocket(ws, data)
