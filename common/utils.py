"""
核心工具类模块

功能：
- HTTP请求处理：支持GET、POST等各种HTTP方法
- WebSocket通信：创建连接、发送消息、接收响应
- 文件操作：YAML文件读取、模板处理
- 数据断言：响应验证、内容检查
- 加密工具：交易解码、哈希计算
- IP模式处理：代理环境下的网络请求

核心类：
- Utils: 主要工具类，包含所有静态方法

主要功能模块：
1. 网络通信：
   - send_http(): HTTP请求发送
   - websocket_connection(): WebSocket连接创建
   - send_websocket(): WebSocket消息发送

2. 文件处理：
   - handle_yaml(): YAML文件读取
   - handle_template(): 模板变量替换

3. 数据验证：
   - assert_status_and_nodeid(): 响应状态断言
   - assert_contains(): 内容包含断言
   - assert_not_contains(): 内容不包含断言

4. 工具函数：
   - tx_decoder(): 交易解码
   - IP模式处理相关方法

使用方式：
    from common.utils import Utils
    response = Utils.send_http(request_data)
    Utils.assert_status_and_nodeid(response)
"""

from string import Template
from typing import Dict, Any, Optional, Union, List, Tuple
from loguru import logger
from websocket import create_connection
import yaml
import json
import httpx
import base64
import hashlib
from urllib.parse import urlparse
from pathlib import Path

from common.handle_path import CONFIG_DIR
from common.ip_mode_handler import IPModeHandler
from common.config_manager import get_config_manager
from common.types import (
    JsonValue, JsonDict, HttpPayload, HttpResponse, PathLike,
    ConfigDict, TestCaseData, RpcRequest, RpcResponse
)

class Utils:
    """
    工具类，提供各种通用方法

    这个类包含了项目中使用的所有通用工具方法，包括：
    - HTTP请求处理
    - 文件读写操作
    - 数据断言验证
    - 字符串模板处理
    - WebSocket通信
    - 加密和哈希计算

    所有方法都是静态方法，可以直接通过类名调用。

    Attributes:
        _ip_mode_handler: IP模式处理器实例
        _config_manager: 配置管理器实例
    """

    # --- 使用新的配置管理器，避免重复读取 ---
    _ip_mode_handler: Optional[IPModeHandler] = None
    _config_manager = get_config_manager()
    # --- 结束缓存 ---

    @classmethod
    def _load_config_and_initialize_ip_handler(cls) -> None:
        """
        加载配置文件并初始化IP模式处理器

        从配置管理器获取配置数据，并初始化IP模式处理器。
        如果配置文件不存在或读取失败，将使用空配置。
        """
        # 使用配置管理器获取配置
        config_data = cls._config_manager.get_config(CONFIG_DIR)
        if not config_data:
            logger.error(f"无法从配置管理器获取配置文件: {CONFIG_DIR}")
            config_data = {}

        # 初始化IP模式处理器
        if cls._ip_mode_handler is None:
            url_paths_mapping = cls._build_url_paths_mapping(config_data)
            cls._ip_mode_handler = IPModeHandler(url_paths_mapping)
    
    @classmethod
    def _build_url_paths_mapping(cls, config_data: Optional[ConfigDict] = None) -> Dict[str, str]:
        """
        构建URL路径到链名的映射

        Args:
            config_data: 配置数据字典，如果为None则从配置管理器获取

        Returns:
            Dict[str, str]: URL路径到链名的映射字典
        """
        if config_data is None:
            config_data = cls._config_manager.get_config(CONFIG_DIR) or {}

        url_paths = {}
        if 'url' in config_data and 'alphanet' in config_data['url']:
            alphanet_config = config_data['url']['alphanet']

            def extract_paths(config_level, base_name=None):
                for key, value in config_level.items():
                    current_name = f"{base_name}_{key}" if base_name else key
                    if isinstance(value, str) and value.startswith(('http://', 'https://')):
                        try:
                            parsed_url = urlparse(value)
                            path = parsed_url.path.rstrip('/')
                            if path:
                                url_paths[path] = current_name
                                logger.trace(f"Config Path Map: '{path}' -> '{current_name}'")
                        except Exception as e:
                            logger.warning(f"解析配置文件 URL '{value}' 失败: {e}")
                    elif isinstance(value, dict):
                        extract_paths(value, current_name)

            extract_paths(alphanet_config)
        return url_paths

    @classmethod
    def send_http(cls, data: HttpPayload) -> HttpResponse:
        """
        发送HTTP请求

        Args:
            data: HTTP请求载荷，包含以下字段：
                - method: HTTP方法 ('get', 'post', 'put', 'delete', 等)
                - url: 请求URL
                - headers: 请求头字典 (可选)
                - params: 查询参数字典 (可选)
                - json: JSON数据 (可选)
                - data: 表单数据 (可选)
                - timeout: 超时时间 (可选)
                - verify: SSL验证 (可选)

        Returns:
            HttpResponse: HTTP响应对象

        Raises:
            Exception: 当请求失败时抛出异常

        Example:
            >>> payload = {
            ...     'method': 'post',
            ...     'url': 'https://api.example.com/rpc',
            ...     'headers': {'Content-Type': 'application/json'},
            ...     'json': {'jsonrpc': '2.0', 'method': 'eth_chainId', 'id': 1}
            ... }
            >>> response = Utils.send_http(payload)
            >>> print(response.status_code)
            200
        """
        # 确保配置和IP模式处理器已初始化
        cls._load_config_and_initialize_ip_handler()

        request_url = data.get('url', '')
        request_headers = data.get('headers', {}).copy() if data.get('headers') else {}
        verify_ssl = True  # 默认启用SSL验证
        host_header_added = False

        # 检查是否为IP模式并应用相应配置
        is_ip_mode, original_env_value = cls._ip_mode_handler.is_ip_mode()
        if is_ip_mode:
            verify_ssl, host_header_added = cls._ip_mode_handler.apply_ip_mode_config(
                request_headers, request_url, original_env_value
            )

        # 准备最终发送的数据
        data_to_send = data.copy()
        data_to_send['headers'] = request_headers

        # 记录请求日志
        cls.__api_log(method=data_to_send.get('method'),
                      url=request_url,
                      headers=request_headers,
                      params=data_to_send.get('params'),
                      json=data_to_send.get('json'),
                      data=data_to_send.get('data'))

        try:
            # 发送请求，使用更新后的 verify_ssl
            response = httpx.request(**data_to_send, timeout=20.0, verify=verify_ssl)
            logger.info(f"响应状态码: {response.status_code}")
            return response
        except httpx.RequestError as e:
            logger.error(f"发送请求失败，原始请求参数为：{data}")
            logger.debug(f"失败请求详情（含处理后headers和verify={verify_ssl}）: {data_to_send}")
            logger.error(f"发生的错误为：{e}")
            raise e

    @classmethod
    def websocket_connection(cls, url: str) -> Any:
        """
        建立WebSocket连接

        Args:
            url: WebSocket连接地址

        Returns:
            WebSocket连接对象

        Raises:
            Exception: 当连接失败时抛出异常

        Example:
            >>> ws = Utils.websocket_connection('wss://api.example.com/ws')
            >>> print(ws.getstatus())
            101
        """
        try:
            ws = create_connection(url, timeout=20)  # 创建连接
            logger.info(f"WebSocket连接成功，响应状态码为：{ws.getstatus()}")
        except Exception as e:
            logger.error(f'创建WebSocket连接失败')
            logger.exception(f'发生的错误为：{e}')
            raise e
        return ws

    @classmethod
    def send_websocket(cls, ws: Any, data: JsonDict) -> JsonDict:
        """
        发送WebSocket消息

        Args:
            ws: WebSocket连接对象
            data: 要发送的JSON数据

        Returns:
            JsonDict: 服务器响应的JSON数据

        Raises:
            Exception: 当发送或接收消息失败时抛出异常

        Example:
            >>> ws = Utils.websocket_connection('wss://api.example.com/ws')
            >>> request_data = {'jsonrpc': '2.0', 'method': 'eth_chainId', 'id': 1}
            >>> response = Utils.send_websocket(ws, request_data)
            >>> print(response['result'])
            '0x1'
        """
        try:
            ws.send(json.dumps(data))  # 发送消息
            logger.info(f'发送数据成功：{data}')
            response = ws.recv()
        except Exception as e:
            logger.error(f'发送数据失败，请求参数为：{data}')
            logger.exception(f'发生的错误为：{e}')
            raise e
        else:
            return response

    @classmethod
    def __api_log(cls, method, url, headers=None, params=None, json=None, data=None):
        """
        记录请求日志
        :param method: 请求方式
        :param url: 请求地址
        :param headers: 请求头
        :param params: 请求参数
        :param json: 请求体(json)
        :param data: 请求体(form-data)
        :return:
        """
        logger.info(f"请求方式：{method}")
        logger.info(f"请求地址：{url}")
        logger.info(f"请求头：{headers if headers else '{}'}") # 确保打印空字典
        logger.info(f"请求参数：{params}")
        logger.info(f"请求体(json)：{json}")
        logger.info(f"请求体(data)：{data}")

    @classmethod
    def handle_yaml(cls, file_name: PathLike) -> Optional[ConfigDict]:
        """
        读取YAML文件 - 使用配置管理器优化性能

        Args:
            file_name: YAML文件路径，支持字符串或Path对象

        Returns:
            Optional[ConfigDict]: YAML文件内容的字典，如果文件不存在或解析失败则返回None

        Raises:
            FileNotFoundError: 当文件不存在时抛出
            Exception: 当文件解析失败时抛出

        Note:
            此方法使用配置管理器的缓存机制，避免重复读取相同文件。
            配置管理器会自动检测文件变更并更新缓存。

        Example:
            >>> config = Utils.handle_yaml('config/config.yaml')
            >>> if config:
            ...     print(config['database']['host'])
            localhost
        """
        try:
            # 使用配置管理器获取配置，支持缓存和热更新
            yaml_data = cls._config_manager.get_config(file_name)
            if yaml_data is None:
                raise FileNotFoundError(f"无法加载配置文件: {file_name}")
            return yaml_data
        except Exception as e:
            logger.error(f'YAML文件读取失败，文件名称：{file_name}, 错误: {e}')
            raise e

    @classmethod
    def handle_template(cls, source_data, replace_data: dict):
        """
        替换文本变量
        :param source_data: 源数据
        :param replace_data: 需要替换的内容
        :return:
        """
        res = Template(str(source_data)).safe_substitute(**replace_data)
        return yaml.safe_load(res)

    @staticmethod
    def assert_status_and_nodeid(response: HttpResponse) -> None:
        """
        断言响应状态码和节点ID

        验证HTTP响应的状态码是否为200，并检查响应头中的X-Node-Id字段。

        Args:
            response: HTTP响应对象

        Raises:
            AssertionError: 当以下情况发生时抛出：
                - 响应状态码不是200
                - 响应头中缺少'X-Node-Id'字段
                - 'X-Node-Id'字段值为空
                - 'X-Node-Id'字段值不包含'0x'

        Example:
            >>> response = requests.get('https://api.example.com/rpc')
            >>> Utils.assert_status_and_nodeid(response)  # 如果验证通过则无返回值
        """
        try:
            assert response.status_code == 200, f"响应状态码错误，预期 200，实际 {response.status_code}"
            # 确保 X-Node-Id 存在且不为空
            assert 'X-Node-Id' in response.headers, "响应头缺少 'X-Node-Id'"
            assert response.headers['X-Node-Id'], "'X-Node-Id' 响应头值为空"
            assert '0x' in response.headers['X-Node-Id'], f"'X-Node-Id' ({response.headers['X-Node-Id']}) 不包含 '0x'"
            logger.info(f"断言成功: 状态码={response.status_code}, X-Node-Id={response.headers.get('X-Node-Id')}")
        except AssertionError as e:
            logger.error(f"eq断言失败，预期结果：200，实际结果：{response.status_code if response else 'N/A'}")
            logger.error(f"用例失败！断言信息: {e}")
            # 可以选择在这里记录更详细的响应信息
            if response is not None:
                logger.debug(f"失败响应详情: Status={response.status_code}, Headers={response.headers}, Body={response.text[:500]}...") # 记录部分响应体
            raise e # 将断言错误向上抛出

    @staticmethod
    def assert_contains(content, expected='0x'):
        """
        断言包含，默认参数0x
        :param content: 文本内容
        :param expected: 目标文本（字符串或字符串列表）或预期结果数组（包含字典）
        """
        try:
            if isinstance(expected, list):
                # 检查列表中的元素类型，以区分“或”逻辑和数组比较
                if all(isinstance(item, str) for item in expected):
                    # 如果expected是字符串列表，执行“或”逻辑
                    content_str = str(content)
                    assert any(item in content_str for item in expected), \
                        f"字符串包含断言失败 - 未找到期望的内容: {expected}\n" \
                        f"实际内容: {content_str[:200]}{'...' if len(content_str) > 200 else ''}"
                else:
                    # 如果expected是包含字典的列表，执行数组结构和内容比较
                    assert isinstance(content, list), \
                        f"数组类型断言失败 - 实际内容不是列表\n" \
                        f"期望类型: list, 实际类型: {type(content).__name__}\n" \
                        f"实际内容: {content}"
                    assert len(content) == len(expected), \
                        f"数组长度断言失败\n" \
                        f"期望长度: {len(expected)}, 实际长度: {len(content)}\n" \
                        f"期望内容: {expected}\n" \
                        f"实际内容: {content}"
                    for exp_item, actual_item in zip(expected, content):
                        if isinstance(exp_item, dict) and 'result' in exp_item:
                            # 处理 result 中包含通配符的情况
                            if isinstance(exp_item['result'], str) and exp_item['result'].endswith('*'):
                                prefix = exp_item['result'][:-1]
                                if isinstance(actual_item, dict) and 'result' in actual_item and isinstance(actual_item['result'], str):
                                    assert actual_item['result'].startswith(prefix), \
                                        f"前缀匹配断言失败\n" \
                                        f"期望前缀: {prefix}\n" \
                                        f"实际值: {actual_item['result']}\n" \
                                        f"完整期望项: {exp_item}\n" \
                                        f"完整实际项: {actual_item}"
                                else:
                                    # 如果 result 是字典（比如区块信息），则跳过前缀检查
                                    assert isinstance(actual_item, dict) and 'result' in actual_item and isinstance(actual_item['result'], dict), \
                                        f"字典结构断言失败\n" \
                                        f"期望: 包含 'result' 字段的字典类型\n" \
                                        f"实际值: {actual_item}\n" \
                                        f"实际类型: {type(actual_item).__name__}"
                            else:
                                assert isinstance(actual_item, dict) and 'result' in actual_item and exp_item['result'] == actual_item['result'], \
                                    f"结果值断言失败\n" \
                                    f"期望结果: {exp_item['result']}\n" \
                                    f"实际结果: {actual_item.get('result', 'MISSING_RESULT_FIELD')}\n" \
                                    f"完整期望项: {exp_item}\n" \
                                    f"完整实际项: {actual_item}"
                        else:
                            # 对于非字典元素，进行直接相等比较
                            assert exp_item == actual_item, \
                                f"数组元素断言失败\n" \
                                f"期望: {exp_item} (类型: {type(exp_item).__name__})\n" \
                                f"实际: {actual_item} (类型: {type(actual_item).__name__})"
            elif isinstance(expected, str):
                # 原有的字符串包含逻辑
                content_str = str(content)
                assert expected in content_str, \
                    f"字符串包含断言失败\n" \
                    f"期望包含: '{expected}'\n" \
                    f"实际内容: {content_str[:300]}{'...' if len(content_str) > 300 else ''}\n" \
                    f"内容长度: {len(content_str)}"
            else:
                raise TypeError(f"参数类型错误 - expected 参数必须是字符串、字符串列表或包含字典的列表\n"
                              f"实际类型: {type(expected).__name__}\n"
                              f"实际值: {expected}")
        except AssertionError as e:
            logger.error(f"assert_contains 断言失败: {e}")
            logger.error("用例失败！")
            raise e
        except Exception as e:
            logger.error(f"assert_contains 执行异常: {type(e).__name__}: {e}")
            logger.error(f"断言参数 - expected: {expected}, content: {content}")
            raise e

    @staticmethod
    def assert_id_and_version(content):
        """
        断言响应id和version
        :param content: 文本内容
        :return:
        """
        try:
            # 检查 content 是否为字典
            if not isinstance(content, dict):
                raise AssertionError(f"响应内容类型错误 - 期望字典类型，实际类型: {type(content).__name__}")
            
            # 检查必需字段是否存在
            missing_fields = []
            if 'id' not in content:
                missing_fields.append('id')
            if 'jsonrpc' not in content:
                missing_fields.append('jsonrpc')
            
            if missing_fields:
                raise AssertionError(f"响应缺少必需字段: {missing_fields}\n"
                                   f"实际字段: {list(content.keys())}\n"
                                   f"完整响应: {content}")
            
            # 检查字段值
            id_value = content['id']
            jsonrpc_value = content['jsonrpc']
            
            id_valid = id_value == 1
            jsonrpc_valid = jsonrpc_value == '2.0'
            
            assert id_valid and jsonrpc_valid, \
                f"JSON-RPC 字段值断言失败\n" \
                f"ID 断言: 期望=1, 实际={id_value} (类型: {type(id_value).__name__}) {'✓' if id_valid else '✗'}\n" \
                f"JSONRPC 断言: 期望='2.0', 实际='{jsonrpc_value}' (类型: {type(jsonrpc_value).__name__}) {'✓' if jsonrpc_valid else '✗'}\n" \
                f"完整响应: {content}"
            
            logger.debug(f"JSON-RPC 字段验证成功: id={id_value}, jsonrpc={jsonrpc_value}")
            
        except AssertionError as e:
            logger.error(f"assert_id_and_version 断言失败: {e}")
            logger.error("用例失败！")
            raise e
        except Exception as e:
            logger.error(f"assert_id_and_version 执行异常: {type(e).__name__}: {e}")
            logger.error(f"断言参数类型: {type(content).__name__}, 内容: {content}")
            raise e

    @staticmethod
    def assert_not_contains(content, expected='error'):
        """
        断言响应文本不包含
        :param content: 文本内容
        :param expected: 目标文本
        """
        try:
            content_str = str(content)
            contains_unexpected = expected in content_str
            
            assert not contains_unexpected, \
                f"不应包含内容断言失败\n" \
                f"不应包含: '{expected}'\n" \
                f"但在内容中发现了: {'是' if contains_unexpected else '否'}\n" \
                f"内容摘要: {content_str[:200]}{'...' if len(content_str) > 200 else ''}\n" \
                f"内容长度: {len(content_str)}\n" \
                f"内容类型: {type(content).__name__}"
            
            logger.debug(f"不包含断言成功: 内容中未发现 '{expected}'")
            
        except AssertionError as e:
            logger.error(f"assert_not_contains 断言失败: {e}")
            logger.error("用例失败！")
            raise e
        except Exception as e:
            logger.error(f"assert_not_contains 执行异常: {type(e).__name__}: {e}")
            logger.error(f"断言参数 - expected: '{expected}', content 类型: {type(content).__name__}")
            raise e

    @staticmethod
    def tx_decoder(encoded_tx):
        """
        解码交易并计算哈希值
        :param encoded_tx: base64编码的交易数据
        :return: 交易哈希值
        """
        decoded_tx = base64.b64decode(encoded_tx)
        tx_hash = hashlib.sha256(decoded_tx).hexdigest()
        return tx_hash

    @staticmethod
    def read_yaml(file_path):
        """
        读取yaml文件
        :param file_path: yaml文件路径
        :return:
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"YAML 文件未找到: {file_path}")
            return None
        except yaml.YAMLError as e:
            logger.error(f"解析 YAML 文件时出错: {file_path}, Error: {e}")
            return None
        except Exception as e:
            logger.error(f"读取 YAML 文件时发生未知错误: {file_path}, Error: {e}")
            return None